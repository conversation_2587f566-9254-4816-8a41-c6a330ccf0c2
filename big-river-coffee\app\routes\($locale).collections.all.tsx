import {type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {useLoaderData, type MetaFunction, useSearchParams, useNavigate} from 'react-router';
import {getPaginationVariables, Image, Money, getSelectedProductOptions, getProductOptions} from '@shopify/hydrogen';
import {PaginatedResourceSection} from '~/components/PaginatedResourceSection';
import {ProductCard} from '~/components/ProductCard';
import {ProductFilters} from '~/components/ProductFilters';
import {AddToCartButton} from '~/components/AddToCartButton';
import {useState, useEffect, useRef} from 'react';
import {useFetcher} from 'react-router';
import {useAside} from '~/components/Aside';
import {OptimizedVideo} from '~/components/OptimizedVideo';
import {initScrollAnimations} from '~/lib/scrollAnimations';

export const meta: MetaFunction<typeof loader> = () => {
  return [
    {title: 'All Coffees | Big River Coffee'},
    {description: 'Discover our complete collection of premium coffee - ethically sourced and expertly roasted for every adventure'}
  ];
};

export async function loader(args: LoaderFunctionArgs) {
  // Start fetching non-critical data without blocking time to first byte
  const deferredData = loadDeferredData(args);

  // Await the critical data required to render initial state of the page
  const criticalData = await loadCriticalData(args);

  return {...deferredData, ...criticalData};
}

export async function action({request, context}: LoaderFunctionArgs) {
  const {storefront} = context;
  const formData = await request.formData();
  const action = formData.get('action');

  if (action === 'fetchProduct') {
    const handle = formData.get('handle') as string;

    if (!handle) {
      return new Response(JSON.stringify({error: 'Product handle is required'}), {
        status: 400,
        headers: {'Content-Type': 'application/json'},
      });
    }

    try {
      // Get selected options from form data or use empty array
      const selectedOptionsData = formData.get('selectedOptions');
      const selectedOptions = selectedOptionsData ? JSON.parse(selectedOptionsData as string) : [];

      const {product} = await storefront.query(INDIVIDUAL_PRODUCT_QUERY, {
        variables: {
          handle,
          country: storefront.i18n.country,
          language: storefront.i18n.language,
          selectedOptions: selectedOptions as any,
        },
      });

      return new Response(JSON.stringify({product}), {
        headers: {'Content-Type': 'application/json'},
      });
    } catch (error) {
      console.error('Error fetching individual product:', error);
      return new Response(JSON.stringify({error: 'Failed to fetch product'}), {
        status: 500,
        headers: {'Content-Type': 'application/json'},
      });
    }
  }

  return new Response(JSON.stringify({error: 'Invalid action'}), {
    status: 400,
    headers: {'Content-Type': 'application/json'},
  });
}

/**
 * Load data necessary for rendering content above the fold. This is the critical data
 * needed to render the page. If it's unavailable, the whole page should 400 or 500 error.
 */
async function loadCriticalData({context, request}: LoaderFunctionArgs) {
  const {storefront} = context;
  const paginationVariables = getPaginationVariables(request, {
    pageBy: 8,
  });

  const [{products}] = await Promise.all([
    storefront.query(CATALOG_QUERY, {
      variables: {...paginationVariables},
    }),
    // Add other queries here, so that they are loaded in parallel
  ]);
  return {products};
}

/**
 * Load data for rendering content below the fold. This data is deferred and will be
 * fetched after the initial page load. If it's unavailable, the page should still 200.
 * Make sure to not throw any errors here, as it will cause the page to 500.
 */
function loadDeferredData({context}: LoaderFunctionArgs) {
  return {};
}

export default function AllCoffees() {
  const {products} = useLoaderData<typeof loader>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [filteredProducts, setFilteredProducts] = useState(products.nodes);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('featured');
  const [showFilters, setShowFilters] = useState(false);
  const [filtersOpen, setFiltersOpen] = useState(true);
  const [activeSection, setActiveSection] = useState('coffee');
  const [isManualScrolling, setIsManualScrolling] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  // Refs for section scrolling
  const coffeeRef = useRef<HTMLDivElement>(null);
  const kcupsRef = useRef<HTMLDivElement>(null);
  const subscriptionsRef = useRef<HTMLDivElement>(null);
  const gearRef = useRef<HTMLDivElement>(null);

  // Get filter values from URL params
  const roast = searchParams.get('roast') || 'all';
  const kcupType = searchParams.get('kcup-type') || 'all';
  const gearCategory = searchParams.get('gear-category') || 'all';
  const priceRange = searchParams.get('price') || 'all';
  const section = searchParams.get('section') || 'coffee';

  // Product categorization function
  const categorizeProduct = (product: any) => {
    const title = product.title.toLowerCase();
    const tags = product.tags || [];

    // Check for K-cups
    if (title.includes('k-cup') || title.includes('kcup') || title.includes('k cup') ||
        tags.some((tag: string) => tag.toLowerCase().includes('k-cup'))) {
      return 'kcups';
    }

    // Check for gear/equipment
    if (title.includes('mug') || title.includes('cup') || title.includes('tumbler') ||
        title.includes('grinder') || title.includes('equipment') || title.includes('gear') ||
        title.includes('shirt') || title.includes('hat') || title.includes('apparel') ||
        tags.some((tag: string) => {
          const tagLower = tag.toLowerCase();
          return tagLower.includes('gear') || tagLower.includes('equipment') ||
                 tagLower.includes('apparel') || tagLower.includes('accessories');
        })) {
      return 'gear';
    }

    // Default to coffee
    return 'coffee';
  };

  // Categorize all products
  const categorizedProducts = {
    coffee: products.nodes.filter(product => categorizeProduct(product) === 'coffee'),
    kcups: products.nodes.filter(product => categorizeProduct(product) === 'kcups'),
    gear: products.nodes.filter(product => categorizeProduct(product) === 'gear'),
  };

  // Filter function for each section
  const getFilteredProducts = (sectionName: string, sectionProducts: any[]) => {
    let filtered = [...sectionProducts];

    // Apply section-specific filters
    if (sectionName === 'coffee' && roast !== 'all') {
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(roast)
      );
    } else if (sectionName === 'kcups' && kcupType !== 'all') {
      filtered = filtered.filter(product => {
        const title = product.title.toLowerCase();
        switch (kcupType) {
          case 'single-origin':
            return title.includes('single') || title.includes('origin');
          case 'blend':
            return title.includes('blend');
          case 'flavored':
            return title.includes('vanilla') || title.includes('caramel') ||
                   title.includes('hazelnut') || title.includes('flavored');
          default:
            return true;
        }
      });
    } else if (sectionName === 'gear' && gearCategory !== 'all') {
      filtered = filtered.filter(product => {
        const title = product.title.toLowerCase();
        switch (gearCategory) {
          case 'mugs':
            return title.includes('mug') || title.includes('cup') || title.includes('tumbler');
          case 'equipment':
            return title.includes('grinder') || title.includes('equipment') || title.includes('machine');
          case 'apparel':
            return title.includes('shirt') || title.includes('hat') || title.includes('apparel');
          case 'accessories':
            return title.includes('accessory') || title.includes('gear') ||
                   (!title.includes('mug') && !title.includes('cup') &&
                    !title.includes('grinder') && !title.includes('shirt'));
          default:
            return true;
        }
      });
    }

    // Apply price range filter
    if (priceRange !== 'all') {
      filtered = filtered.filter(product => {
        const price = parseFloat(product.priceRange.minVariantPrice.amount);
        switch (priceRange) {
          case 'under-25':
            return price < 25;
          case '25-50':
            return price >= 25 && price <= 50;
          case 'over-50':
            return price > 50;
          default:
            return true;
        }
      });
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) =>
          parseFloat(a.priceRange.minVariantPrice.amount) - parseFloat(b.priceRange.minVariantPrice.amount)
        );
        break;
      case 'price-high':
        filtered.sort((a, b) =>
          parseFloat(b.priceRange.minVariantPrice.amount) - parseFloat(a.priceRange.minVariantPrice.amount)
        );
        break;
      case 'name':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      default:
        // Keep original order for 'featured'
        break;
    }

    return filtered;
  };

  // Update active section when URL changes (but not during manual scrolling)
  useEffect(() => {
    if (!isManualScrolling) {
      setActiveSection(section);
    }
  }, [section, isManualScrolling]);

  // Handle section parameter from URL and scroll to section
  useEffect(() => {
    const urlSection = searchParams.get('section');
    if (urlSection && ['coffee', 'kcups', 'subscriptions', 'gear'].includes(urlSection)) {
      setActiveSection(urlSection);

      // Scroll to the section after a short delay to ensure elements are rendered
      setTimeout(() => {
        const targetRef = urlSection === 'coffee' ? coffeeRef :
                         urlSection === 'kcups' ? kcupsRef :
                         urlSection === 'subscriptions' ? subscriptionsRef : gearRef;

        if (targetRef.current) {
          const headerOffset = 180; // Account for larger sticky header and navigation
          const elementPosition = targetRef.current.offsetTop;
          const offsetPosition = elementPosition - headerOffset;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });
        }
      }, 300); // Increased delay to ensure page is fully loaded
    }
  }, [searchParams]);

  // Scroll spy to update active section based on scroll position
  useEffect(() => {
    let scrollTimeout: NodeJS.Timeout;
    let isScrolling = false;

    const handleScroll = () => {
      // Skip scroll spy during manual scrolling
      if (isManualScrolling || isScrolling) return;

      isScrolling = true;

      // Clear previous timeout
      clearTimeout(scrollTimeout);

      // Debounce scroll events to avoid conflicts
      scrollTimeout = setTimeout(() => {
        try {
          const sections = [
            { name: 'coffee', ref: coffeeRef },
            { name: 'kcups', ref: kcupsRef },
            { name: 'subscriptions', ref: subscriptionsRef },
            { name: 'gear', ref: gearRef },
          ];

          const scrollTop = window.pageYOffset;
          const headerOffset = 180;
          let newActiveSection = activeSection;

          // Find which section is currently in view
          for (const section of sections) {
            if (section.ref.current) {
              const sectionTop = section.ref.current.offsetTop - headerOffset;
              const sectionBottom = sectionTop + section.ref.current.offsetHeight;

              if (scrollTop >= sectionTop && scrollTop < sectionBottom) {
                newActiveSection = section.name;
                break;
              }
            }
          }

          // Update active section if it changed
          if (newActiveSection !== activeSection) {
            setActiveSection(newActiveSection);
            // Update URL without triggering navigation
            const newParams = new URLSearchParams(searchParams);
            newParams.set('section', newActiveSection);
            window.history.replaceState({}, '', `?${newParams.toString()}`);
          }
        } catch (error) {
          console.error('Scroll handling error:', error);
        } finally {
          isScrolling = false;
        }
      }, 150); // Increased debounce time
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, [activeSection, searchParams, isManualScrolling]);

  // Ensure navigation UI stability after hydration and on resize
  useEffect(() => {
    const updateNavDisplay = () => {
      const navElement = document.querySelector('.collections-mobile-nav');
      if (navElement) {
        (navElement as HTMLElement).style.display = window.innerWidth < 1024 ? 'block' : 'none';
        (navElement as HTMLElement).style.visibility = 'visible';
      }
      const desktopNavElement = document.querySelector('.collections-desktop-nav');
      if (desktopNavElement) {
        (desktopNavElement as HTMLElement).style.display = window.innerWidth >= 1024 ? 'flex' : 'none';
        (desktopNavElement as HTMLElement).style.visibility = 'visible';
      }
    };

    // Multiple updates to ensure stability
    const timer1 = setTimeout(updateNavDisplay, 10);
    const timer2 = setTimeout(updateNavDisplay, 100);
    const timer3 = setTimeout(updateNavDisplay, 500);

    // Listen for window resize
    window.addEventListener('resize', updateNavDisplay);

    // Also listen for page visibility changes
    document.addEventListener('visibilitychange', updateNavDisplay);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      window.removeEventListener('resize', updateNavDisplay);
      document.removeEventListener('visibilitychange', updateNavDisplay);
    };
  }, []);

  const handleSortChange = (newSort: string) => {
    setSortBy(newSort);
  };

  // Section navigation functions
  const scrollToSection = (sectionName: string) => {
    try {
      // Disable scroll spy during manual scrolling
      setIsManualScrolling(true);

      // Update active section immediately
      setActiveSection(sectionName);

      // Get the target element
      const refs = { coffee: coffeeRef, kcups: kcupsRef, subscriptions: subscriptionsRef, gear: gearRef };
      const targetRef = refs[sectionName as keyof typeof refs];

      if (targetRef.current) {
        // Calculate scroll position manually for better control
        const headerOffset = 200; // Increased to account for both header and nav
        const elementPosition = targetRef.current.offsetTop;
        const offsetPosition = Math.max(0, elementPosition - headerOffset);

        // Update URL immediately
        const newParams = new URLSearchParams(searchParams);
        newParams.set('section', sectionName);
        window.history.replaceState({}, '', `?${newParams.toString()}`);

        // Scroll to the calculated position
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });

        // Re-enable scroll spy after scrolling is complete
        setTimeout(() => {
          setIsManualScrolling(false);
        }, 1000);

      } else {
        console.warn(`Section ref not found for: ${sectionName}`);
        setIsManualScrolling(false);
      }
    } catch (error) {
      console.error('Error in scrollToSection:', error);
      setIsManualScrolling(false);
    }
  };

  // Get section display info
  const getSectionInfo = (sectionName: string) => {
    const counts = {
      coffee: categorizedProducts.coffee.length,
      kcups: categorizedProducts.kcups.length,
      subscriptions: 1, // Static count for subscription section
      gear: categorizedProducts.gear.length,
    };

    const info = {
      coffee: {
        title: 'Coffee',
        description: 'Premium coffee beans, ethically sourced and expertly roasted',
        icon: (
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M2 21h18v-2H2v2zM20 8h-2V5c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v8c0 2.21 1.79 4 4 4h6c2.21 0 4-1.79 4-4v-3h2c1.1 0 2-.9 2-2s-.9-2-2-2zM16 13c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2V5h12v8z"/>
            <path d="M20 10c.55 0 1-.45 1-1s-.45-1-1-1v2z"/>
          </svg>
        ),
        count: counts.coffee,
      },
      kcups: {
        title: 'K-Cups',
        description: 'Convenient single-serve coffee pods for your Keurig',
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        ),
        count: counts.kcups,
      },
      gear: {
        title: 'Gear',
        description: 'Coffee equipment, mugs, and adventure gear',
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        ),
        count: counts.gear,
      },
      subscriptions: {
        title: 'Subscriptions',
        description: 'Never run out of coffee with our flexible subscription service',
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        ),
        count: counts.subscriptions,
      },
    };

    return info[sectionName as keyof typeof info];
  };

  // Collections page body class
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Add collections page class to body
    document.body.classList.add('collections-page');

    // Cleanup
    return () => {
      document.body.classList.remove('collections-page');
    };
  }, []);

  // Initialize scroll animations
  useEffect(() => {
    const cleanup = initScrollAnimations();
    return cleanup;
  }, []);

  return (
    <>
      {/* Collections page styling */}
      <style>{`
        body.collections-page {
          margin: 0 !important;
          padding: 0 !important;
        }
        body.collections-page main {
          padding: 0 !important;
          margin: 0 !important;
        }
      `}</style>

      {/* Hero Video Section - Desktop Only */}
      <div className="hidden sm:block sticky top-0 w-full h-screen z-0">
        {/* Desktop Hero Video */}
        <div className="w-full h-full">
          <OptimizedVideo
            src="/newhomepage/shop_hero_vid.mp4"
            className="w-full h-full object-cover"
            style={{
              width: '100%',
              height: '100%',
              display: 'block'
            }}
            autoPlay={true}
            muted={true}
            loop={true}
            playsInline={true}
            preload="auto"
            lazy={false}
          />
        </div>
      </div>

      {/* Shop Content Section */}
      <div className="relative z-10 min-h-screen bg-gray-50" style={{ marginTop: '0', paddingTop: '0' }}>

      {/* Section Navigation */}
      <div className="sticky z-40 bg-white border-b border-neutral-200 shadow-sm top-0">
        <div className="container-clean">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between py-4 gap-4">
            {/* Section Buttons */}
            <div className="flex-1">
              {/* Desktop Layout */}
              <div className="hidden lg:flex space-x-2">
                {['coffee', 'kcups', 'subscriptions', 'gear'].map((sectionName) => {
                  const sectionInfo = getSectionInfo(sectionName);
                  const isActive = activeSection === sectionName;

                  return (
                    <button
                      key={`desktop-${sectionName}`}
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        scrollToSection(sectionName);
                      }}
                      className={`relative flex items-center px-4 py-2 rounded-lg font-semibold transition-all duration-300 whitespace-nowrap ${
                        isActive
                          ? 'bg-amber-500 text-white shadow-lg'
                          : 'text-neutral-600 hover:text-amber-500 hover:bg-amber-50'
                      }`}
                    >
                      <span className="mr-3 flex-shrink-0">{sectionInfo.icon}</span>
                      <span className="mr-3">{sectionInfo.title}</span>
                      <span className={`text-xs px-2 py-1 rounded-full font-medium flex-shrink-0 ${
                        isActive
                          ? 'bg-white/20 text-white'
                          : 'bg-army-100 text-army-800'
                      }`}>
                        {sectionInfo.count}
                      </span>
                    </button>
                  );
                })}
              </div>

              {/* Mobile Layout */}
              <div className="lg:hidden">
                <div className="grid grid-cols-2 gap-3">
                  {['coffee', 'kcups', 'subscriptions', 'gear'].map((sectionName) => {
                    const sectionInfo = getSectionInfo(sectionName);
                    const isActive = activeSection === sectionName;

                    return (
                      <button
                        key={`mobile-${sectionName}`}
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          scrollToSection(sectionName);
                        }}
                        className={`flex items-center justify-center px-3 py-3 rounded-lg font-medium transition-all duration-200 ${
                          isActive
                            ? 'bg-amber-500 text-white shadow-lg'
                            : 'bg-white text-neutral-700 border border-neutral-200 hover:border-amber-300 hover:text-amber-600'
                        }`}
                      >
                        <span className="mr-2 flex-shrink-0">{sectionInfo.icon}</span>
                        <span className="text-sm font-semibold">{sectionInfo.title}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Desktop Controls Only */}
            <div className="hidden lg:flex items-center space-x-4">
              {/* Sort Controls - Desktop Only */}
              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value)}
                className="border border-neutral-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-army-500 min-w-[140px]"
              >
                <option value="featured">Featured</option>
                <option value="name">Name A-Z</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
              </select>

              {/* View Toggle Buttons - Desktop Only */}
              <div className="flex items-center border border-neutral-300 rounded-lg overflow-hidden bg-white shadow-sm">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`relative flex items-center justify-center w-12 h-12 transition-all duration-200 ${
                    viewMode === 'grid'
                      ? 'bg-amber-500 text-white shadow-sm'
                      : 'bg-white text-neutral-600 hover:bg-neutral-50 hover:text-amber-500'
                  }`}
                  title="Grid View"
                  aria-label="Grid View"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                  </svg>
                </button>
                <div className="w-px h-6 bg-neutral-300"></div>
                <button
                  onClick={() => setViewMode('list')}
                  className={`relative flex items-center justify-center w-12 h-12 transition-all duration-200 ${
                    viewMode === 'list'
                      ? 'bg-amber-500 text-white shadow-sm'
                      : 'bg-white text-neutral-600 hover:bg-neutral-50 hover:text-amber-500'
                  }`}
                  title="List View"
                  aria-label="List View"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Sectioned Layout */}
      <div className="container-clean py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar - Desktop - TEMPORARILY HIDDEN */}
          {/*
          <div className="hidden lg:block w-80 flex-shrink-0">
            <div className={activeSection === 'coffee' ? 'sticky top-44' : ''}>
              <ProductFilters
                isOpen={filtersOpen}
                onToggle={() => setFiltersOpen(!filtersOpen)}
                activeSection={activeSection}
              />
            </div>
          </div>
          */}

          {/* Products Section */}
          <div className="flex-1">
            {/* Mobile Filters - TEMPORARILY HIDDEN */}
            {/*
            {showFilters && (
              <div className={`lg:hidden mb-8 bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden z-30 ${
                activeSection === 'coffee' ? 'sticky top-24' : ''
              }`}>
                <ProductFilters
                  isOpen={true}
                  onToggle={() => setShowFilters(false)}
                  activeSection={activeSection}
                />
              </div>
            )}
            */}

            {/* All Sections - Always Visible */}
            {['coffee', 'kcups', 'subscriptions', 'gear'].map((sectionName) => {
              const sectionInfo = getSectionInfo(sectionName);
              const sectionProducts = sectionName === 'subscriptions' ? [] : categorizedProducts[sectionName as keyof typeof categorizedProducts];
              const filteredSectionProducts = sectionName === 'subscriptions' ? [] : getFilteredProducts(sectionName, sectionProducts);

              // Special layout for K-cups section to match product page
              if (sectionName === 'kcups') {
                // For K-cups, we'll create a product page-style layout
                const kcupProduct = filteredSectionProducts[0]; // Use first K-cup product for demo

                if (kcupProduct && filteredSectionProducts.length > 0) {
                  // Use actual K-cup variants instead of mock data
                  const allVariants = kcupProduct.variants?.nodes || [];
                  const availableVariants = allVariants.filter((variant: any) => variant.availableForSale);
                  const defaultVariant = availableVariants[0] || allVariants[0];

                  // Safety check - if no variants exist, don't render the section
                  if (!defaultVariant) {
                    return null;
                  }



                  const [selectedVariant, setSelectedVariant] = useState(defaultVariant);
                  const [quantity, setQuantity] = useState(1);

                  // Helper function to get variant display name
                  const getVariantDisplayName = (variant: any) => {
                    if (variant.title && variant.title !== 'Default Title') {
                      return variant.title;
                    }
                    // Try to get flavor from selectedOptions - K-cups should have flavor variants
                    const flavorOption = variant.selectedOptions?.find((opt: any) =>
                      opt.name.toLowerCase().includes('flavor') ||
                      opt.name.toLowerCase().includes('title') ||
                      opt.name.toLowerCase().includes('variant') ||
                      opt.name.toLowerCase().includes('type')
                    );
                    if (flavorOption?.value) {
                      return flavorOption.value;
                    }
                    // Fallback to variant title or a generic name
                    return variant.title || `K-Cup Flavor ${variant.id.split('/').pop()}`;
                  };

                  return (
                    <div
                      key={sectionName}
                      ref={kcupsRef}
                      className="mb-20"
                      id={`section-${sectionName}`}
                      style={{ scrollMarginTop: '140px' }}
                    >
                      {/* Section Header */}
                      <div className="mb-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-army-100 rounded-xl flex items-center justify-center mr-3 text-army-600">
                              {sectionInfo.icon}
                            </div>
                            <div>
                              <h2 className={`text-3xl font-bold transition-colors duration-200 ${
                                activeSection === sectionName ? 'text-amber-600' : 'text-gray-900'
                              }`} style={{ fontFamily: 'var(--font-title)' }}>
                                {sectionInfo.title}
                              </h2>
                              <p className="text-gray-600">{sectionInfo.description}</p>
                            </div>
                          </div>
                        </div>

                        {/* Section divider */}
                        <div className={`h-1 rounded-full transition-all duration-300 ${
                          activeSection === sectionName
                            ? 'bg-amber-600 w-24'
                            : 'bg-gray-200 w-16'
                        }`} />
                      </div>

                      {/* K-Cup Product Card - Mobile Optimized */}
                      <div className="bg-gradient-to-r from-army-50 to-army-100 rounded-xl border border-army-200 p-4 sm:p-6 mb-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
                          {/* Product Image - Responsive with proper sizing */}
                          <div className="kcup-image-container aspect-square bg-white rounded-xl overflow-hidden shadow-sm max-w-sm mx-auto lg:max-w-md lg:mx-0">
                            {kcupProduct.featuredImage ? (
                              <img
                                src={kcupProduct.featuredImage.url}
                                alt={kcupProduct.featuredImage.altText || kcupProduct.title}
                                className="w-full h-full object-contain p-4"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <svg className="w-16 h-16 sm:w-24 sm:h-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                </svg>
                              </div>
                            )}
                          </div>

                          {/* Product Info */}
                          <div className="flex flex-col justify-between space-y-4">
                            <div>
                              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2" style={{ fontFamily: 'var(--font-title)' }}>Big River K-Cups</h3>
                              <p className="text-sm sm:text-base text-gray-600 mb-4">20 count single-serve pods • Compatible with Keurig 1.0 & 2.0</p>
                              <div className="text-2xl sm:text-3xl font-bold text-army-700 mb-4 sm:mb-6">
                                <Money data={kcupProduct.priceRange.minVariantPrice} />
                              </div>

                              {/* Flavor Selection - Mobile Optimized */}
                              <div className="mb-4 sm:mb-6">
                                <label className="text-base sm:text-lg font-semibold text-gray-900 mb-3 block">Flavors</label>
                                {availableVariants.length > 0 ? (
                                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                                    {availableVariants.map((variant: any) => {
                                      const displayName = getVariantDisplayName(variant);
                                      return (
                                        <button
                                          key={variant.id}
                                          onClick={() => setSelectedVariant(variant)}
                                          className={`px-3 sm:px-4 py-3 border rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 text-left min-h-[48px] ${
                                            selectedVariant?.id === variant.id
                                              ? 'border-amber-600 bg-amber-600 text-white shadow-md'
                                              : 'border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600'
                                          }`}
                                        >
                                          {displayName}
                                        </button>
                                      );
                                    })}
                                  </div>
                                ) : allVariants.length > 0 ? (
                                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                                    {allVariants.map((variant: any) => {
                                      const displayName = getVariantDisplayName(variant);
                                      return (
                                        <button
                                          key={variant.id}
                                          onClick={() => setSelectedVariant(variant)}
                                          disabled={!variant.availableForSale}
                                          className={`px-3 sm:px-4 py-3 border rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 text-left min-h-[48px] ${
                                            selectedVariant?.id === variant.id
                                              ? 'border-amber-600 bg-amber-600 text-white shadow-md'
                                              : variant.availableForSale
                                              ? 'border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600'
                                              : 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed'
                                          }`}
                                        >
                                          {displayName} {!variant.availableForSale && '(Out of Stock)'}
                                        </button>
                                      );
                                    })}
                                  </div>
                                ) : (
                                  <div className="text-gray-500 text-sm">No flavor options available</div>
                                )}
                              </div>
                            </div>

                            {/* Quantity and Add to Cart */}
                            <div className="space-y-4">
                              <div className="flex items-center justify-between sm:justify-start sm:space-x-4">
                                <span className="text-base sm:text-lg font-semibold text-gray-900">Quantity:</span>
                                <div className="flex items-center space-x-3">
                                  <button
                                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                                    className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors min-h-[44px] min-w-[44px]"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                    </svg>
                                  </button>
                                  <span className="text-lg sm:text-xl font-semibold text-gray-900 min-w-[3rem] text-center">{quantity}</span>
                                  <button
                                    onClick={() => setQuantity(quantity + 1)}
                                    className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors min-h-[44px] min-w-[44px]"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                  </button>
                                </div>
                              </div>

                              <AddToCartButton
                                disabled={!selectedVariant || !selectedVariant.availableForSale || !selectedVariant.id}
                                lines={selectedVariant?.id ? [{
                                  merchandiseId: selectedVariant.id,
                                  quantity: quantity,
                                }] : []}
                                className="w-full bg-amber-600 hover:bg-amber-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-3 sm:py-4 px-4 sm:px-6 rounded-lg font-semibold text-base sm:text-lg transition-colors duration-200 min-h-[48px]"
                              >
                                {!selectedVariant ? 'Select a flavor' : selectedVariant.availableForSale ? 'Add to cart' : 'Out of stock'}
                              </AddToCartButton>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                }
              }

              // Special layout for Subscriptions section
              if (sectionName === 'subscriptions') {
                const [selectedProductType, setSelectedProductType] = useState('');
                const [selectedCoffeeProduct, setSelectedCoffeeProduct] = useState('');
                const [selectedCoffeeSize, setSelectedCoffeeSize] = useState('');
                const [selectedCoffeeType, setSelectedCoffeeType] = useState('');
                const [selectedKcupFlavor, setSelectedKcupFlavor] = useState('');
                // Build your own bundle flavor selections
                const [selectedFlavor1, setSelectedFlavor1] = useState('');
                const [selectedFlavor2, setSelectedFlavor2] = useState('');
                const [selectedFlavor3, setSelectedFlavor3] = useState('');
                const [quantity, setQuantity] = useState(1);
                const [frequency, setFrequency] = useState('monthly');
                const [showFrequencyDropdown, setShowFrequencyDropdown] = useState(false);
                const frequencyDropdownRef = useRef<HTMLDivElement>(null);
                const fetcher = useFetcher<{product?: any; error?: string}>();
                const {open} = useAside();

                // Close frequency dropdown when clicking outside
                useEffect(() => {
                  const handleClickOutside = (event: MouseEvent) => {
                    if (frequencyDropdownRef.current && !frequencyDropdownRef.current.contains(event.target as Node)) {
                      setShowFrequencyDropdown(false);
                    }
                  };

                  document.addEventListener('mousedown', handleClickOutside);
                  return () => {
                    document.removeEventListener('mousedown', handleClickOutside);
                  };
                }, []);

                // Helper functions
                const getCoffeeProducts = () => {
                  return categorizedProducts.coffee || [];
                };

                const fetchIndividualProduct = (productHandle: string, selectedOptions: any[] = []) => {
                  const formData = new FormData();
                  formData.append('action', 'fetchProduct');
                  formData.append('handle', productHandle);
                  formData.append('selectedOptions', JSON.stringify(selectedOptions));

                  fetcher.submit(formData, {
                    method: 'POST',
                  });
                };

                const getKcupProduct = () => {
                  // Find the K-cup product from the categorized products
                  return categorizedProducts.kcups?.[0] || null;
                };

                // Get product options using Hydrogen's utility (for fetched product)
                const getHydrogenProductOptions = (product: any) => {
                  if (!product) return [];
                  try {
                    return getProductOptions({
                      ...product,
                      selectedOrFirstAvailableVariant: product.selectedOrFirstAvailableVariant || product.variants?.nodes?.[0]
                    });
                  } catch (error) {
                    console.error('Error getting product options:', error);
                    return [];
                  }
                };

                const findVariantBySelectedOptions = (product: any, selectedOptions: Record<string, string>) => {
                  if (!product?.variants?.nodes) return null;
                  return product.variants.nodes.find((variant: any) => {
                    return Object.entries(selectedOptions).every(([optionName, optionValue]) => {
                      return variant.selectedOptions?.some((opt: any) =>
                        opt.name.toLowerCase() === optionName.toLowerCase() && opt.value === optionValue
                      );
                    });
                  });
                };

                const handleProductTypeChange = (productType: string) => {
                  setSelectedProductType(productType);
                  setSelectedCoffeeProduct('');
                  setSelectedCoffeeSize('');
                  setSelectedCoffeeType('');
                  setSelectedKcupFlavor('');
                  setSelectedFlavor1('');
                  setSelectedFlavor2('');
                  setSelectedFlavor3('');
                };

                const coffeeProducts = getCoffeeProducts();
                const kcupProduct = getKcupProduct();
                const selectedCoffeeProductData = coffeeProducts.find(p => p.id === selectedCoffeeProduct);

                // Fetch individual product data when coffee product is selected
                useEffect(() => {
                  if (selectedCoffeeProduct && selectedCoffeeProductData) {
                    setSelectedCoffeeSize('');
                    setSelectedCoffeeType('');
                    // Fetch with empty selected options initially
                    fetchIndividualProduct(selectedCoffeeProductData.handle, []);
                  }
                }, [selectedCoffeeProduct]);



                const frequencyOptions = [
                  { value: 'weekly', label: '1 Week', sellingPlanId: 'gid://shopify/SellingPlan/9581953339' },
                  { value: 'monthly', label: '1 Month', sellingPlanId: 'gid://shopify/SellingPlan/9581986107' },
                  { value: '3weeks', label: '3 Weeks', sellingPlanId: 'gid://shopify/SellingPlan/9582018875' },
                  { value: '6weeks', label: '6 Weeks', sellingPlanId: 'gid://shopify/SellingPlan/9582051643' }
                ];

                const selectedFrequencyOption = frequencyOptions.find(f => f.value === frequency) || frequencyOptions[1];

                // Generate subscription URL based on selections
                const getSubscriptionUrl = () => {
                  const selectedFrequencyOption = frequencyOptions.find(f => f.value === frequency);
                  if (!selectedFrequencyOption) return null;

                  if (selectedProductType === 'coffee' && selectedCoffeeProduct && selectedCoffeeSize && selectedCoffeeType && fetcher.data?.product) {
                    const variant = findVariantBySelectedOptions(fetcher.data.product, {
                      Size: selectedCoffeeSize,
                      type: selectedCoffeeType
                    });
                    if (variant) {
                      return `/products/${fetcher.data.product.handle}?variant=${variant.id}&selling_plan=${selectedFrequencyOption.sellingPlanId}&quantity=${quantity}`;
                    }
                  } else if (selectedProductType === 'kcups' && selectedKcupFlavor && kcupProduct) {
                    return `/products/${kcupProduct.handle}?variant=${selectedKcupFlavor}&selling_plan=${selectedFrequencyOption.sellingPlanId}&quantity=${quantity}`;
                  }
                  return null;
                };

                return (
                  <div
                    key={sectionName}
                    ref={subscriptionsRef}
                    className="mb-20"
                    id={`section-${sectionName}`}
                    style={{ scrollMarginTop: '140px' }}
                  >
                    {/* Section Header */}
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-army-100 rounded-xl flex items-center justify-center mr-3 text-army-600">
                            {sectionInfo.icon}
                          </div>
                          <div>
                            <h2 className={`text-3xl font-bold transition-colors duration-200 ${
                              activeSection === sectionName ? 'text-amber-600' : 'text-gray-900'
                            }`} style={{ fontFamily: 'var(--font-title)' }}>
                              Big River Coffee Subscriptions
                            </h2>
                            <p className="text-gray-600">Never run out of coffee with our flexible subscription service</p>
                          </div>
                        </div>
                      </div>

                      {/* Section divider */}
                      <div className={`h-1 rounded-full transition-all duration-300 ${
                        activeSection === sectionName
                          ? 'bg-amber-600 w-24'
                          : 'bg-gray-200 w-16'
                      }`} />
                    </div>

                    {/* Subscription Card - Horizontal Layout */}
                    <div className="bg-gradient-to-r from-army-50 to-army-100 rounded-xl border border-army-200 p-6 mb-6">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Subscription Image */}
                        <div className="aspect-square bg-white rounded-xl overflow-hidden shadow-sm">
                          <img
                            src="/subscriptionimg.webp"
                            alt="Big River Coffee Subscription"
                            className="w-full h-full object-center object-cover"
                          />
                        </div>

                        {/* Subscription Info */}
                        <div className="flex flex-col justify-between">
                          <div>
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'var(--font-title)' }}>Big River Coffee Subscription</h3>
                              <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                15% OFF
                              </span>
                            </div>
                            <p className="text-gray-600 mb-4">Fresh coffee delivered to your doorstep • Save 15% on every order • Free shipping on orders over $30</p>

                            {/* Benefits */}
                            <div className="mb-6 space-y-2">
                              <div className="flex items-center text-sm text-gray-700">
                                <svg className="w-4 h-4 mr-2 text-army-600" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                                Save your money with up to 15% off
                              </div>
                              <div className="flex items-center text-sm text-gray-700">
                                <svg className="w-4 h-4 mr-2 text-army-600" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                                Delay, modify & cancel anytime
                              </div>
                              <div className="flex items-center text-sm text-gray-700">
                                <svg className="w-4 h-4 mr-2 text-army-600" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                                Simplify your month with automatic delivery
                              </div>
                            </div>

                            {/* Product Selection */}
                            <div className="mb-6">
                              <label className="text-lg font-semibold text-gray-900 mb-3 block">Choose Your Product</label>
                              <div className="grid grid-cols-2 gap-3">
                                <button
                                  onClick={() => handleProductTypeChange('coffee')}
                                  className={`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200 text-left ${
                                    selectedProductType === 'coffee'
                                      ? 'border-army-600 bg-army-600 text-white shadow-md'
                                      : 'border-gray-300 bg-white text-gray-700 hover:border-army-500 hover:text-army-600'
                                  }`}
                                >
                                  Coffee Beans
                                </button>
                                <button
                                  onClick={() => handleProductTypeChange('kcups')}
                                  className={`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200 text-left ${
                                    selectedProductType === 'kcups'
                                      ? 'border-amber-600 bg-amber-600 text-white shadow-md'
                                      : 'border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600'
                                  }`}
                                >
                                  K-Cups
                                </button>
                              </div>
                            </div>

                            {/* Coffee Product Selection - Shows after selecting Coffee */}
                            {selectedProductType === 'coffee' && (
                              <div className="mb-6 transition-all duration-200">
                                <label className="text-lg font-semibold text-gray-900 mb-3 block">Select Coffee Product</label>
                                <div className="relative">
                                  <select
                                    value={selectedCoffeeProduct}
                                    onChange={(e) => {
                                      setSelectedCoffeeProduct(e.target.value);
                                      setSelectedCoffeeSize('');
                                      setSelectedCoffeeType('');
                                      setSelectedFlavor1('');
                                      setSelectedFlavor2('');
                                      setSelectedFlavor3('');
                                    }}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200"
                                  >
                                    <option value="">Choose a coffee...</option>
                                    {coffeeProducts.map((product) => (
                                      <option key={product.id} value={product.id}>
                                        {product.title}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              </div>
                            )}

                            {/* Coffee Variant Options - Shows after selecting a coffee product */}
                            {selectedProductType === 'coffee' && selectedCoffeeProduct && (
                              <div className="mb-6 transition-all duration-200">
                                {fetcher.state === 'submitting' || fetcher.state === 'loading' ? (
                                  <div className="flex items-center justify-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"></div>
                                    <span className="ml-3 text-gray-600">Loading product options...</span>
                                  </div>
                                ) : fetcher.data?.product ? (
                                  <div className="space-y-4">
                                    {(() => {
                                      const productOptions = getHydrogenProductOptions(fetcher.data.product);




                                      // Check if this is the build your own bundle, blend box, or roasters box
                                      const isBuildYourOwnBundle = fetcher.data.product.id === 'gid://shopify/Product/8965076156731' ||
                                        fetcher.data.product.title.toLowerCase().includes('build your own');

                                      const isBlendBox = fetcher.data.product.id === 'gid://shopify/Product/10111589941563' ||
                                        fetcher.data.product.title.toLowerCase().includes('blend box');

                                      const isRoastersBox = fetcher.data.product.id === 'gid://shopify/Product/10111587647803' ||
                                        fetcher.data.product.title.toLowerCase().includes('roasters box');

                                      // Handle Build Your Own Bundle
                                      if (isBuildYourOwnBundle || productOptions.some(opt => opt.name.includes('Flavor #'))) {
                                          // Get available flavors from Flavor #1 option (they should all have the same options)
                                        const flavor1Option = productOptions.find(opt => opt.name === 'Flavor #1');
                                        const availableFlavors = flavor1Option?.optionValues?.map(val => val.name) || [];

                                        return (
                                          <div className="space-y-4">
                                            <div className="text-center mb-4">
                                              <h4 className="text-lg font-semibold text-gray-900 mb-2">Build Your Own Bundle</h4>
                                              <p className="text-sm text-gray-600">Pick 3 flavors for your custom coffee bundle</p>
                                            </div>

                                            {/* Flavor Selection Grid */}
                                            <div className="grid grid-cols-3 gap-4">
                                              {/* Flavor #1 */}
                                              <div>
                                                <label className="text-sm font-medium text-gray-700 mb-2 block">Flavor #1</label>
                                                <select
                                                  value={selectedFlavor1}
                                                  onChange={(e) => setSelectedFlavor1(e.target.value)}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200 text-sm"
                                                >
                                                  <option value="">Choose...</option>
                                                  {availableFlavors.map((flavor) => (
                                                    <option key={flavor} value={flavor}>
                                                      {flavor}
                                                    </option>
                                                  ))}
                                                </select>
                                              </div>

                                              {/* Flavor #2 */}
                                              <div>
                                                <label className="text-sm font-medium text-gray-700 mb-2 block">Flavor #2</label>
                                                <select
                                                  value={selectedFlavor2}
                                                  onChange={(e) => setSelectedFlavor2(e.target.value)}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200 text-sm"
                                                >
                                                  <option value="">Choose...</option>
                                                  {availableFlavors.map((flavor) => (
                                                    <option key={flavor} value={flavor}>
                                                      {flavor}
                                                    </option>
                                                  ))}
                                                </select>
                                              </div>

                                              {/* Flavor #3 */}
                                              <div>
                                                <label className="text-sm font-medium text-gray-700 mb-2 block">Flavor #3</label>
                                                <select
                                                  value={selectedFlavor3}
                                                  onChange={(e) => setSelectedFlavor3(e.target.value)}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200 text-sm"
                                                >
                                                  <option value="">Choose...</option>
                                                  {availableFlavors.map((flavor) => (
                                                    <option key={flavor} value={flavor}>
                                                      {flavor}
                                                    </option>
                                                  ))}
                                                </select>
                                              </div>
                                            </div>

                                            {/* Selected Combination Display */}
                                            {selectedFlavor1 && selectedFlavor2 && selectedFlavor3 && (
                                              <div className="bg-army-50 border border-army-200 rounded-lg p-3">
                                                <p className="text-sm font-medium text-army-800">
                                                  Your Bundle: {selectedFlavor1} + {selectedFlavor2} + {selectedFlavor3}
                                                </p>
                                              </div>
                                            )}
                                          </div>
                                        );
                                      }

                                      // Handle Blend Box products
                                      if (isBlendBox) {
                                        const boxSelectionOption = productOptions.find(opt => opt.name === 'Box Selection');
                                        const typeOption = productOptions.find(opt => opt.name === 'Type');

                                        return (
                                          <div className="space-y-4">
                                            <div className="text-center mb-4">
                                              <h4 className="text-lg font-semibold text-gray-900 mb-2">Blend Box Selection</h4>
                                              <p className="text-sm text-gray-600">Choose your blend combination</p>
                                            </div>

                                            {/* Box Selection */}
                                            {boxSelectionOption && (
                                              <div>
                                                <label className="text-sm font-medium text-gray-700 mb-2 block">Blend Combination</label>
                                                <select
                                                  value={selectedCoffeeSize} // Reusing this state for box selection
                                                  onChange={(e) => setSelectedCoffeeSize(e.target.value)}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200"
                                                >
                                                  <option value="">Choose combination...</option>
                                                  {boxSelectionOption.optionValues.map((value) => (
                                                    <option key={value.name} value={value.name}>
                                                      {value.name}
                                                    </option>
                                                  ))}
                                                </select>
                                              </div>
                                            )}

                                            {/* Type Selection */}
                                            {typeOption && (
                                              <div>
                                                <label className="text-sm font-medium text-gray-700 mb-2 block">Grind Type</label>
                                                <select
                                                  value={selectedCoffeeType}
                                                  onChange={(e) => setSelectedCoffeeType(e.target.value)}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200"
                                                >
                                                  <option value="">Choose grind type...</option>
                                                  {typeOption.optionValues.map((value) => (
                                                    <option key={value.name} value={value.name}>
                                                      {value.name}
                                                    </option>
                                                  ))}
                                                </select>
                                              </div>
                                            )}
                                          </div>
                                        );
                                      }

                                      // Handle Roasters Box products - simplified to one variant type
                                      if (isRoastersBox) {
                                        // Try different option names that might exist for Roasters Box
                                        const typeOption = productOptions.find(opt =>
                                          opt.name === 'Type' ||
                                          opt.name === 'Grind' ||
                                          opt.name === 'Grind Type' ||
                                          opt.name.toLowerCase().includes('type') ||
                                          opt.name.toLowerCase().includes('grind')
                                        );

                                        return (
                                          <div className="space-y-4">
                                            <div className="text-center mb-4">
                                              <h4 className="text-lg font-semibold text-gray-900 mb-2">Roasters Box Selection</h4>
                                              <p className="text-sm text-gray-600">Select your grind preference</p>
                                            </div>

                                            {/* Type Selection Only */}
                                            {typeOption ? (
                                              <div>
                                                <label className="text-sm font-medium text-gray-700 mb-2 block">
                                                  {typeOption.name}
                                                </label>
                                                <select
                                                  value={selectedCoffeeType}
                                                  onChange={(e) => setSelectedCoffeeType(e.target.value)}
                                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200"
                                                >
                                                  <option value="">Choose {typeOption.name.toLowerCase()}...</option>
                                                  {typeOption.optionValues.map((value) => (
                                                    <option key={value.name} value={value.name}>
                                                      {value.name}
                                                    </option>
                                                  ))}
                                                </select>
                                              </div>
                                            ) : (
                                              <div className="text-gray-500 text-sm">
                                                No variant options available for this product.
                                              </div>
                                            )}
                                          </div>
                                        );
                                      }

                                      // Regular coffee products with Size and type options
                                      const sizeOption = productOptions.find(opt => opt.name === 'Size');
                                      const typeOption = productOptions.find(opt =>
                                        opt.name === 'type' || opt.name === 'Type'
                                      );

                                      return (
                                        <>
                                          {/* Size Selection */}
                                          {sizeOption && (
                                            <div>
                                              <label className="text-sm font-medium text-gray-700 mb-2 block">Size</label>
                                              <select
                                                value={selectedCoffeeSize}
                                                onChange={(e) => setSelectedCoffeeSize(e.target.value)}
                                                className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200"
                                              >
                                                <option value="">Choose size...</option>
                                                {sizeOption.optionValues.map((value) => (
                                                  <option key={value.name} value={value.name}>
                                                    {value.name}
                                                  </option>
                                                ))}
                                              </select>
                                            </div>
                                          )}

                                          {/* Type Selection - Only show if the option exists */}
                                          {typeOption && typeOption.optionValues && typeOption.optionValues.length > 0 && (
                                            <div>
                                              <label className="text-sm font-medium text-gray-700 mb-2 block">
                                                {typeOption.name === 'type' ? 'Type' : typeOption.name}
                                              </label>
                                              <select
                                                value={selectedCoffeeType}
                                                onChange={(e) => setSelectedCoffeeType(e.target.value)}
                                                className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200"
                                              >
                                                <option value="">Choose {typeOption.name.toLowerCase()}...</option>
                                                {typeOption.optionValues.map((value) => (
                                                  <option key={value.name} value={value.name}>
                                                    {value.name}
                                                  </option>
                                                ))}
                                              </select>
                                            </div>
                                          )}


                                        </>
                                      );
                                    })()}
                                  </div>
                                ) : fetcher.data?.error ? (
                                  <div className="text-center py-4 text-red-500">
                                    Error loading product options: {fetcher.data.error}
                                  </div>
                                ) : (
                                  <div className="text-center py-4 text-gray-500">
                                    Select a coffee product to see available options
                                  </div>
                                )}
                              </div>
                            )}

                            {/* K-Cup Flavor Selection - Shows after selecting K-Cups */}
                            {selectedProductType === 'kcups' && kcupProduct && (
                              <div className="mb-6 transition-all duration-200">
                                <label className="text-lg font-semibold text-gray-900 mb-3 block">Select Flavor</label>
                                <div className="grid grid-cols-2 gap-3">
                                  {kcupProduct.variants?.nodes?.map((variant: any) => (
                                    <button
                                      key={variant.id}
                                      onClick={() => setSelectedKcupFlavor(variant.id)}
                                      className={`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200 text-left ${
                                        selectedKcupFlavor === variant.id
                                          ? 'border-amber-600 bg-amber-600 text-white shadow-md'
                                          : 'border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600'
                                      }`}
                                    >
                                      {variant.selectedOptions?.find((opt: any) => opt.name.toLowerCase().includes('title') || opt.name.toLowerCase().includes('flavor'))?.value || variant.title || 'K-Cup Variant'}
                                    </button>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Frequency Selection - Shows after complete product selection */}
                            {selectedProductType && (
                              (selectedProductType === 'coffee' && selectedCoffeeProduct && (
                                // Regular coffee products
                                (selectedCoffeeSize && selectedCoffeeType) ||
                                // Build your own bundle
                                (selectedFlavor1 && selectedFlavor2 && selectedFlavor3) ||
                                // Roasters Box (only needs type selection)
                                (fetcher.data?.product && (fetcher.data.product.id === 'gid://shopify/Product/10111587647803' ||
                                 fetcher.data.product.title.toLowerCase().includes('roasters box')) && selectedCoffeeType)
                              )) ||
                              (selectedProductType === 'kcups' && selectedKcupFlavor)
                            ) && (
                              <div className="mb-6 transition-all duration-200">
                                <label className="text-lg font-semibold text-gray-900 mb-3 block">Delivery Frequency</label>
                                <div className="relative" ref={frequencyDropdownRef}>
                                  <button
                                    type="button"
                                    onClick={() => setShowFrequencyDropdown(!showFrequencyDropdown)}
                                    className="w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 hover:border-gray-400 transition-colors duration-200"
                                  >
                                    <div className="flex items-center justify-between">
                                      <span className="font-medium text-gray-900">{selectedFrequencyOption.label}</span>
                                      <svg
                                        className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
                                          showFrequencyDropdown ? 'transform rotate-180' : ''
                                        }`}
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                      >
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                      </svg>
                                    </div>
                                  </button>

                                  {/* Dropdown Options */}
                                  {showFrequencyDropdown && (
                                    <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
                                      {frequencyOptions.map((option) => (
                                        <button
                                          key={option.value}
                                          type="button"
                                          onClick={() => {
                                            setFrequency(option.value);
                                            setShowFrequencyDropdown(false);
                                          }}
                                          className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 first:rounded-t-lg last:rounded-b-lg ${
                                            frequency === option.value ? 'bg-army-50 border-l-4 border-army-500' : ''
                                          }`}
                                        >
                                          <span className={`font-medium ${
                                            frequency === option.value ? 'text-army-900' : 'text-gray-900'
                                          }`}>
                                            {option.label}
                                          </span>
                                        </button>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Quantity and Subscribe Button - Only show after all variants are selected */}
                          {selectedProductType && (
                            (selectedProductType === 'coffee' && selectedCoffeeProduct && (
                              // Regular coffee products
                              (selectedCoffeeSize && selectedCoffeeType) ||
                              // Build your own bundle
                              (selectedFlavor1 && selectedFlavor2 && selectedFlavor3) ||
                              // Roasters Box (only needs type selection)
                              (fetcher.data?.product && (fetcher.data.product.id === 'gid://shopify/Product/10111587647803' ||
                               fetcher.data.product.title.toLowerCase().includes('roasters box')) && selectedCoffeeType)
                            )) ||
                            (selectedProductType === 'kcups' && selectedKcupFlavor)
                          ) && (
                            <div className="space-y-4">
                              <div className="flex items-center space-x-4">
                                <span className="text-lg font-semibold text-gray-900">Quantity:</span>
                                <div className="flex items-center space-x-3">
                                  <button
                                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                                    className="w-10 h-10 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                    </svg>
                                  </button>
                                  <span className="text-xl font-semibold text-gray-900 min-w-[3rem] text-center">{quantity}</span>
                                  <button
                                    onClick={() => setQuantity(quantity + 1)}
                                    className="w-10 h-10 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                  </button>
                                </div>
                              </div>

                              <div className="space-y-3">
                                {/* Subscribe Button - Only show when valid selections are made */}
                                {(() => {
                                  const selectedFrequencyOption = frequencyOptions.find(f => f.value === frequency);
                                  let variant = null;

                                  if (selectedProductType === 'coffee' && selectedCoffeeProduct && fetcher.data?.product) {
                                    // Check product types
                                    const isBuildYourOwnBundle = fetcher.data.product.id === 'gid://shopify/Product/8965076156731' ||
                                      getHydrogenProductOptions(fetcher.data.product).some(opt => opt.name.includes('Flavor #'));

                                    const isBlendBox = fetcher.data.product.id === 'gid://shopify/Product/10111589941563';
                                    const isRoastersBox = fetcher.data.product.id === 'gid://shopify/Product/10111587647803';

                                    if (isBuildYourOwnBundle && selectedFlavor1 && selectedFlavor2 && selectedFlavor3) {
                                      // For build your own bundle, find variant by exact flavor combination
                                      variant = fetcher.data.product.variants?.nodes?.find((v: any) => {
                                        const options = v.selectedOptions || [];
                                        const flavor1Match = options.find((opt: any) => opt.name === 'Flavor #1')?.value === selectedFlavor1;
                                        const flavor2Match = options.find((opt: any) => opt.name === 'Flavor #2')?.value === selectedFlavor2;
                                        const flavor3Match = options.find((opt: any) => opt.name === 'Flavor #3')?.value === selectedFlavor3;

                                        return flavor1Match && flavor2Match && flavor3Match;
                                      });
                                    } else if (isBlendBox && selectedCoffeeSize && selectedCoffeeType) {
                                      // For Blend Box, find variant by Box Selection and Type only
                                      variant = fetcher.data.product.variants?.nodes?.find((v: any) => {
                                        const options = v.selectedOptions || [];
                                        const boxSelectionMatch = options.find((opt: any) => opt.name === 'Box Selection')?.value === selectedCoffeeSize;
                                        const typeMatch = options.find((opt: any) => opt.name === 'Type')?.value === selectedCoffeeType;

                                        return boxSelectionMatch && typeMatch;
                                      });
                                    } else if (isRoastersBox && selectedCoffeeType) {
                                      // For Roasters Box, find variant by Type only (simplified)
                                      // Try different option names that might exist for Roasters Box
                                      variant = fetcher.data.product.variants?.nodes?.find((v: any) => {
                                        const options = v.selectedOptions || [];
                                        const typeMatch = options.find((opt: any) =>
                                          (opt.name === 'Type' ||
                                           opt.name === 'Grind' ||
                                           opt.name === 'Grind Type' ||
                                           opt.name.toLowerCase().includes('type') ||
                                           opt.name.toLowerCase().includes('grind')) &&
                                          opt.value === selectedCoffeeType
                                        );

                                        return typeMatch;
                                      });
                                    } else if (selectedCoffeeSize && selectedCoffeeType) {
                                      // Regular coffee products with Size and type
                                      variant = findVariantBySelectedOptions(fetcher.data.product, {
                                        Size: selectedCoffeeSize,
                                        type: selectedCoffeeType
                                      });
                                    }
                                  } else if (selectedProductType === 'kcups' && selectedKcupFlavor && kcupProduct) {
                                    variant = kcupProduct.variants?.nodes?.find((v: any) => v.id === selectedKcupFlavor);
                                  }

                                  if (variant && selectedFrequencyOption) {
                                    return (
                                      <AddToCartButton
                                        onClick={() => open('cart')}
                                        lines={[{
                                          merchandiseId: variant.id,
                                          quantity: quantity,
                                          sellingPlanId: selectedFrequencyOption.sellingPlanId,
                                        }]}
                                        className={`w-full py-4 px-6 rounded-lg font-semibold text-lg transition-colors duration-200 text-white ${
                                          selectedProductType === 'coffee'
                                            ? 'bg-army-600 hover:bg-army-700'
                                            : 'bg-amber-600 hover:bg-amber-700'
                                        }`}
                                      >
                                        Start Subscription
                                      </AddToCartButton>
                                    );
                                  }

                                  // Debug message when variant is not found
                                  if (selectedProductType === 'coffee' && selectedCoffeeProduct && selectedFrequencyOption && !variant) {
                                    // Check if we have the required selections for different product types
                                    const hasRegularCoffeeSelections = selectedCoffeeSize && selectedCoffeeType;
                                    const hasBundleSelections = selectedFlavor1 && selectedFlavor2 && selectedFlavor3;
                                    const hasBlendBoxSelections = selectedCoffeeSize && selectedCoffeeType; // Blend box uses same as regular
                                    const hasRoastersBoxSelections = (fetcher.data?.product && (fetcher.data.product.id === 'gid://shopify/Product/10111587647803' ||
                                      fetcher.data.product.title.toLowerCase().includes('roasters box')) && selectedCoffeeType); // Roasters box only needs type

                                    if (hasRegularCoffeeSelections || hasBundleSelections || hasBlendBoxSelections || hasRoastersBoxSelections) {
                                      return (
                                        <div className="text-center py-4 text-red-500 text-sm">
                                          Unable to find matching variant. Please try different selections.
                                          <br />
                                          <span className="text-xs text-gray-500">
                                            Product: {fetcher.data?.product?.title || 'Unknown'} |
                                            Selections: Size={selectedCoffeeSize}, Type={selectedCoffeeType},
                                            Flavors={selectedFlavor1},{selectedFlavor2},{selectedFlavor3}
                                          </span>
                                        </div>
                                      );
                                    }
                                  }

                                  // Debug: Show what's missing for the button to appear
                                  if (selectedProductType === 'coffee' && selectedCoffeeProduct) {
                                    return (
                                      <div className="text-center py-4 text-gray-500 text-xs">
                                        Debug: Product selected but no button.
                                        <br />Product: {selectedCoffeeProductData?.title}
                                        <br />Fetcher data: {fetcher.data?.product ? 'Yes' : 'No'}
                                        <br />Selections: Size={selectedCoffeeSize}, Type={selectedCoffeeType}
                                        <br />Frequency: {selectedFrequencyOption?.label}
                                        <br />Variant found: {variant ? 'Yes' : 'No'}
                                      </div>
                                    );
                                  }

                                  return null;
                                })()}

                              </div>
                            </div>
                          )}

                          {/* Learn More Button - Always visible outside conditional */}
                          <div className="mt-6">
                            <a
                              href="/pages/subscriptions"
                              className="block w-full text-center bg-army-600 hover:bg-army-700 py-3 px-6 rounded-lg font-medium transition-colors duration-200"
                              style={{ color: 'white' }}
                            >
                              <span className="text-white">Learn More About Subscriptions</span>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              }

              // Standard layout for Coffee and Gear sections
              return (
                <div
                  key={sectionName}
                  ref={sectionName === 'coffee' ? coffeeRef : gearRef}
                  className="mb-20"
                  id={`section-${sectionName}`}
                  style={{ scrollMarginTop: '140px' }}
                >
                  {/* Section Header */}
                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-army-100 rounded-xl flex items-center justify-center mr-4 text-army-600">
                          {sectionInfo.icon}
                        </div>
                        <div>
                          <h2 className={`text-4xl font-bold transition-colors duration-200 ${
                            activeSection === sectionName ? 'text-amber-600' : 'text-gray-900'
                          }`} style={{ fontFamily: 'var(--font-title)' }}>
                            {sectionInfo.title}
                          </h2>
                          <p className="text-lg text-gray-600 mt-1">{sectionInfo.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        {/* TEMPORARILY HIDDEN - Filter button
                        {activeSection === sectionName && (
                          <button
                            onClick={() => setShowFilters(!showFilters)}
                            className="lg:hidden inline-flex items-center justify-center px-4 py-2 text-sm font-semibold rounded-lg bg-army-600 text-white border-2 border-army-600 min-h-[44px]"
                          >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                            </svg>
                            Filters
                          </button>
                        )}
                        */}
                        <span className={`px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200 ${
                          activeSection === sectionName
                            ? 'bg-amber-100 text-amber-800'
                            : 'bg-army-100 text-army-800'
                        }`}>
                          {sectionName === 'subscriptions' ? '1 service' : `${filteredSectionProducts.length} ${filteredSectionProducts.length === 1 ? 'item' : 'items'}`}
                        </span>
                      </div>
                    </div>

                    {/* Section divider */}
                    <div className={`h-1 rounded-full transition-all duration-300 ${
                      activeSection === sectionName
                        ? 'bg-amber-600 w-24'
                        : 'bg-gray-200 w-16'
                    }`} />
                  </div>

                  {/* Products Grid */}
                  {(filteredSectionProducts?.length || 0) > 0 ? (
                    <div className={`grid gap-6 ${
                      viewMode === 'grid'
                        ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
                        : 'grid-cols-1'
                    }`}>
                      {filteredSectionProducts.map((product, index) => {
                        // Force Blend box and Roasters box products to show as available
                        const modifiedProduct = (product.id === 'gid://shopify/Product/10111589941563' ||
                                               product.id === 'gid://shopify/Product/10111587647803')
                          ? { ...product, availableForSale: true }
                          : product;

                        return (
                          <ProductCard
                            key={product.id}
                            product={modifiedProduct}
                            loading={index < 6 ? 'eager' : 'lazy'}
                            viewMode={viewMode}
                          />
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-16">
                      <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6 text-gray-400">
                        <div className="w-12 h-12">
                          {sectionInfo.icon}
                        </div>
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        No {sectionInfo.title.toLowerCase()} found
                      </h3>
                      <p className="text-gray-600 mb-6">
                        {(sectionProducts?.length || 0) === 0
                          ? `We don't have any ${sectionInfo.title.toLowerCase()} products yet.`
                          : 'Try adjusting your filters to see more results.'
                        }
                      </p>
                      {(sectionProducts?.length || 0) > 0 && (
                        <button
                          onClick={() => {
                            const newParams = new URLSearchParams();
                            const section = searchParams.get('section');
                            if (section) {
                              newParams.set('section', section);
                            }
                            navigate(`?${newParams.toString()}`);
                          }}
                          className="btn-primary"
                        >
                          Clear All Filters
                        </button>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Nicaraguan Coffee Section */}
        <div className="bg-army-600 py-20 scroll-fade-in">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center text-white">
              <div className="mb-8">
                <span className="inline-flex items-center text-amber-300 text-sm font-medium mb-4">
                  <img src="/coffeeicon.svg" alt="Coffee" className="w-5 h-5 mr-2 filter brightness-0 invert" />
                  Our Origin Story
                </span>
                <h2 className="text-3xl md:text-4xl font-bold mb-6" style={{ fontFamily: 'var(--font-title)' }}>Why Nicaragua?</h2>
                <div className="w-24 h-1 bg-amber-500 rounded mx-auto mb-8"></div>
              </div>

              <div className="grid md:grid-cols-2 gap-8 text-left">
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-amber-300">Perfect Growing Conditions</h3>
                  <p className="text-lg leading-relaxed opacity-90">
                    Nicaragua's volcanic soil, high altitude regions, and tropical climate create ideal conditions
                    for coffee cultivation. The mountainous regions provide perfect elevation and temperature
                    variations that allow coffee cherries to develop slowly, concentrating their flavors.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold mb-4 text-amber-300">Exceptional Flavor Profile</h3>
                  <p className="text-lg leading-relaxed opacity-90">
                    Nicaraguan coffee is renowned for its well-balanced flavor profile, featuring bright acidity,
                    medium to full body, and complex flavor notes ranging from chocolate and nuts to fruity
                    and floral undertones - perfect for any brewing method.
                  </p>
                </div>
              </div>

              <div className="mt-12 p-8 bg-army-700 rounded-xl">
                <p className="text-xl leading-relaxed">
                  <strong>Every cup of Big River Coffee tells the story of Nicaragua's rich coffee heritage,
                  sustainable farming practices, and the passionate farmers who make it all possible.</strong>
                </p>
              </div>
            </div>
          </div>
        </div>


      </div>
      </div> {/* Close the scrollable content overlay */}
    </>
  );
}

const COLLECTION_ITEM_FRAGMENT = `#graphql
  fragment MoneyCollectionItem on MoneyV2 {
    amount
    currencyCode
  }
  fragment ProductVariantCollectionItem on ProductVariant {
    availableForSale
    compareAtPrice {
      ...MoneyCollectionItem
    }
    id
    image {
      __typename
      id
      url
      altText
      width
      height
    }
    price {
      ...MoneyCollectionItem
    }
    product {
      title
      handle
    }
    selectedOptions {
      name
      value
    }
    sku
    title
    unitPrice {
      ...MoneyCollectionItem
    }
  }
  fragment CollectionItem on Product {
    id
    handle
    title
    availableForSale
    featuredImage {
      id
      altText
      url
      width
      height
    }
    priceRange {
      minVariantPrice {
        ...MoneyCollectionItem
      }
      maxVariantPrice {
        ...MoneyCollectionItem
      }
    }
    options {
      name
      optionValues {
        name
        firstSelectableVariant {
          ...ProductVariantCollectionItem
        }
        swatch {
          color
          image {
            previewImage {
              url
            }
          }
        }
      }
    }
    variants(first: 20) {
      nodes {
        ...ProductVariantCollectionItem
      }
    }
  }
` as const;

// Product fragment for individual product fetching (matches product page structure)
const INDIVIDUAL_PRODUCT_VARIANT_FRAGMENT = `#graphql
  fragment IndividualProductVariant on ProductVariant {
    availableForSale
    compareAtPrice {
      amount
      currencyCode
    }
    id
    image {
      __typename
      id
      url
      altText
      width
      height
    }
    price {
      amount
      currencyCode
    }
    product {
      title
      handle
    }
    selectedOptions {
      name
      value
    }
    sellingPlanAllocations(first: 10) {
      nodes {
        sellingPlan {
          id
          name
        }
        priceAdjustments {
          price {
            amount
            currencyCode
          }
          compareAtPrice {
            amount
            currencyCode
          }
        }
      }
    }
    sku
    title
    unitPrice {
      amount
      currencyCode
    }
  }
` as const;

const INDIVIDUAL_PRODUCT_FRAGMENT = `#graphql
  fragment IndividualProduct on Product {
    id
    title
    vendor
    handle
    descriptionHtml
    description
    encodedVariantExistence
    encodedVariantAvailability
    sellingPlanGroups(first: 10) {
      nodes {
        name
        options {
          name
          values
        }
        sellingPlans(first: 10) {
          nodes {
            id
            name
            description
            options {
              name
              value
            }
            priceAdjustments {
              orderCount
              adjustmentValue {
                ... on SellingPlanFixedAmountPriceAdjustment {
                  adjustmentAmount {
                    amount
                    currencyCode
                  }
                }
                ... on SellingPlanPercentagePriceAdjustment {
                  adjustmentPercentage
                }
              }
            }
          }
        }
      }
    }
    options {
      name
      optionValues {
        name
        firstSelectableVariant {
          ...IndividualProductVariant
        }
        swatch {
          color
          image {
            previewImage {
              url
            }
          }
        }
      }
    }
    selectedOrFirstAvailableVariant(selectedOptions: $selectedOptions, ignoreUnknownOptions: true, caseInsensitiveMatch: true) {
      ...IndividualProductVariant
    }
    adjacentVariants(selectedOptions: $selectedOptions) {
      ...IndividualProductVariant
    }
    variants(first: 50) {
      nodes {
        ...IndividualProductVariant
      }
    }
  }
  ${INDIVIDUAL_PRODUCT_VARIANT_FRAGMENT}
` as const;

const INDIVIDUAL_PRODUCT_QUERY = `#graphql
  query IndividualProduct(
    $country: CountryCode
    $handle: String!
    $language: LanguageCode
    $selectedOptions: [SelectedOptionInput!]!
  ) @inContext(country: $country, language: $language) {
    product(handle: $handle) {
      ...IndividualProduct
    }
  }
  ${INDIVIDUAL_PRODUCT_FRAGMENT}
` as const;

// NOTE: https://shopify.dev/docs/api/storefront/latest/objects/product
const CATALOG_QUERY = `#graphql
  query Catalog(
    $country: CountryCode
    $language: LanguageCode
    $first: Int
    $last: Int
    $startCursor: String
    $endCursor: String
  ) @inContext(country: $country, language: $language) {
    products(first: $first, last: $last, before: $startCursor, after: $endCursor) {
      nodes {
        ...CollectionItem
      }
      pageInfo {
        hasPreviousPage
        hasNextPage
        startCursor
        endCursor
      }
    }
  }
  ${COLLECTION_ITEM_FRAGMENT}
` as const;
